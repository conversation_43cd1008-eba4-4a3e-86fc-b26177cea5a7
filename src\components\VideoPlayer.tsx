import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Upload,
  Link,
  Maximize,
  SkipBack,
  SkipForward
} from "lucide-react";
import { extractYouTubeVideoId, isYouTubeUrl } from "@/utils/youtube";
import { useRoomVideoState } from "@/hooks/useRoomVideoState";

interface VideoPlayerProps {
  roomId: string;
  isMuted: boolean;
  onMuteToggle: () => void;
  isOwner: boolean;
  onPlayingStateChange?: (isPlaying: boolean) => void;
}

const VideoPlayer = ({ roomId, isMuted, onMuteToggle, isOwner, onPlayingStateChange }: VideoPlayerProps) => {
  const { videoState, loading, setVideoUrl, setPlaying, setCurrentTime, setDuration } = useRoomVideoState(roomId, isOwner);
  const [inputUrl, setInputUrl] = useState("");
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [volume, setVolume] = useState(1);
  const [youTubeApiStatus, setYouTubeApiStatus] = useState<'loading' | 'loaded' | 'failed' | 'idle'>('idle');
  const videoRef = useRef<HTMLVideoElement>(null);
  const youTubePlayerRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Derived state from videoState
  const hasVideo = !!videoState?.video_url;
  const isPlaying = videoState?.is_playing || false;
  const currentTime = videoState?.video_current_time || 0;
  const duration = videoState?.video_duration || 0;
  const isYouTubeVideo = videoState?.video_type === 'youtube';
  const youTubeVideoId = videoState?.youtube_video_id;

  // Notify parent component of playing state changes
  useEffect(() => {
    onPlayingStateChange?.(isPlaying);
  }, [isPlaying, onPlayingStateChange]);

  // Sync video playback with room state
  useEffect(() => {
    if (isYouTubeVideo && youTubePlayerRef.current) {
      try {
        if (isPlaying && typeof youTubePlayerRef.current.playVideo === 'function') {
          youTubePlayerRef.current.playVideo();
        } else if (!isPlaying && typeof youTubePlayerRef.current.pauseVideo === 'function') {
          youTubePlayerRef.current.pauseVideo();
        }
      } catch (error) {
        console.error('Error controlling YouTube player playback:', error);
      }
    } else if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.play().catch(error => {
          console.error('Error playing video:', error);
        });
      } else {
        videoRef.current.pause();
      }
    }
  }, [isPlaying, isYouTubeVideo]);

  // Sync video time with room state
  useEffect(() => {
    if (isYouTubeVideo && youTubePlayerRef.current) {
      try {
        if (typeof youTubePlayerRef.current.getCurrentTime === 'function' &&
            typeof youTubePlayerRef.current.seekTo === 'function') {
          const playerTime = youTubePlayerRef.current.getCurrentTime();
          if (Math.abs(playerTime - currentTime) > 2) { // Only sync if difference > 2 seconds
            youTubePlayerRef.current.seekTo(currentTime, true);
          }
        }
      } catch (error) {
        console.error('Error syncing YouTube player time:', error);
      }
    } else if (videoRef.current) {
      try {
        const playerTime = videoRef.current.currentTime;
        if (Math.abs(playerTime - currentTime) > 2) { // Only sync if difference > 2 seconds
          videoRef.current.currentTime = currentTime;
        }
      } catch (error) {
        console.error('Error syncing video time:', error);
      }
    }
  }, [currentTime, isYouTubeVideo]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted;
    }

    // Handle YouTube player mute
    if (isYouTubeVideo && youTubePlayerRef.current) {
      try {
        if (isMuted && typeof youTubePlayerRef.current.mute === 'function') {
          youTubePlayerRef.current.mute();
        } else if (!isMuted && typeof youTubePlayerRef.current.unMute === 'function') {
          youTubePlayerRef.current.unMute();
        }
      } catch (error) {
        console.error('Error controlling YouTube player mute:', error);
      }
    }
  }, [isMuted, isYouTubeVideo]);

  // Load YouTube iframe API with error handling and retry mechanism
  useEffect(() => {
    if (isYouTubeVideo && !window.YT) {
      setYouTubeApiStatus('loading');
      loadYouTubeAPI();
    } else if (isYouTubeVideo && window.YT) {
      setYouTubeApiStatus('loaded');
      initializeYouTubePlayer();
    }

    // Cleanup function
    return () => {
      if (youTubePlayerRef.current && typeof youTubePlayerRef.current.destroy === 'function') {
        try {
          youTubePlayerRef.current.destroy();
          youTubePlayerRef.current = null;
        } catch (error) {
          console.error('Error destroying YouTube player:', error);
        }
      }
    };
  }, [isYouTubeVideo, youTubeVideoId]);

  const loadYouTubeAPI = (retryCount = 0) => {
    // Check if API is already loaded
    if (window.YT && window.YT.Player) {
      setYouTubeApiStatus('loaded');
      initializeYouTubePlayer();
      return;
    }

    // Check if script is already being loaded
    const existingScript = document.querySelector('script[src*="youtube.com/iframe_api"]');
    if (existingScript && retryCount === 0) {
      // Script already exists, wait for it to load
      const checkAPI = () => {
        if (window.YT && window.YT.Player) {
          setYouTubeApiStatus('loaded');
          initializeYouTubePlayer();
        } else {
          setTimeout(checkAPI, 100);
        }
      };
      checkAPI();
      return;
    }

    const script = document.createElement('script');
    const maxRetries = 3;
    const retryDelay = 1500; // 1.5 seconds

    // Improved loading strategies to handle CORS issues
    const loadStrategies = [
      // Standard HTTPS with minimal attributes
      () => {
        script.src = 'https://www.youtube.com/iframe_api';
        script.async = true;
        script.defer = true;
      },
      // Alternative approach without crossOrigin
      () => {
        script.src = 'https://www.youtube.com/iframe_api';
        script.async = true;
        // Remove crossOrigin to avoid CORS preflight
      },
      // Try with different referrer policy
      () => {
        script.src = 'https://www.youtube.com/iframe_api';
        script.referrerPolicy = 'strict-origin-when-cross-origin';
        script.async = true;
      }
    ];

    // Apply the current loading strategy
    const currentStrategy = loadStrategies[retryCount % loadStrategies.length];
    currentStrategy();

    script.async = true;
    script.defer = true;

    // Add error handling for script loading
    script.onerror = () => {
      console.warn(`YouTube API load attempt ${retryCount + 1} failed`);

      // Remove the failed script
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }

      // Try next strategy or retry
      if (retryCount < maxRetries) {
        setTimeout(() => {
          loadYouTubeAPI(retryCount + 1);
        }, retryDelay * Math.pow(1.5, retryCount));
      } else {
        setYouTubeApiStatus('failed');
        console.error('Failed to load YouTube iframe API after multiple attempts. This may be due to network restrictions or certificate issues.');
        // Note: Could implement iframe fallback here if needed
      }
    };

    script.onload = () => {
      // API script loaded, but YT object might not be ready yet
      const checkYTReady = (attempts = 0) => {
        if (window.YT && window.YT.Player) {
          setYouTubeApiStatus('loaded');
          initializeYouTubePlayer();
        } else if (attempts < 50) { // Wait up to 5 seconds
          setTimeout(() => checkYTReady(attempts + 1), 100);
        } else {
          setYouTubeApiStatus('failed');
          console.warn('YouTube API loaded but YT.Player not available');
        }
      };
      checkYTReady();
    };

    // Remove any existing failed scripts
    const oldScripts = document.querySelectorAll('script[src*="youtube.com/iframe_api"], script[src*="googleapis.com/youtube"]');
    oldScripts.forEach(oldScript => {
      if (oldScript.parentNode && oldScript !== script) {
        oldScript.parentNode.removeChild(oldScript);
      }
    });

    // Insert script using the recommended method from YouTube docs
    const firstScriptTag = document.getElementsByTagName('script')[0];
    if (firstScriptTag && firstScriptTag.parentNode) {
      firstScriptTag.parentNode.insertBefore(script, firstScriptTag);
    } else {
      document.head.appendChild(script);
    }

    // Set up the API ready callback with timeout
    window.onYouTubeIframeAPIReady = () => {
      if (window.YT && window.YT.Player) {
        initializeYouTubePlayer();
      }
    };

    // Fallback timeout in case the callback never fires
    setTimeout(() => {
      if (window.YT && window.YT.Player && !youTubePlayerRef.current) {
        initializeYouTubePlayer();
      }
    }, 8000); // Increased timeout to 8 seconds
  };

  const initializeYouTubePlayer = () => {
    if (!youTubeVideoId || !window.YT || !window.YT.Player) {
      console.warn('Cannot initialize YouTube player: missing videoId or YT API');
      return;
    }

    // Check if player container exists
    const playerContainer = document.getElementById('youtube-player');
    if (!playerContainer) {
      console.warn('YouTube player container not found');
      return;
    }

    // Destroy existing player if it exists
    if (youTubePlayerRef.current && typeof youTubePlayerRef.current.destroy === 'function') {
      try {
        youTubePlayerRef.current.destroy();
        youTubePlayerRef.current = null;
      } catch (error) {
        console.error('Error destroying existing YouTube player:', error);
      }
    }

    try {
      youTubePlayerRef.current = new window.YT.Player('youtube-player', {
        height: '100%',
        width: '100%',
        videoId: youTubeVideoId,
        playerVars: {
          autoplay: 0,
          controls: 0,
          disablekb: 1,
          fs: 0,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          origin: window.location.origin, // Add origin for security
          enablejsapi: 1, // Enable JavaScript API
        },
        events: {
          onReady: (event: any) => {
            console.log('YouTube player ready');
            try {
              if (isMuted) {
                event.target.mute();
              }
              const videoDuration = event.target.getDuration();
              if (isOwner && videoDuration > 0) {
                setDuration(videoDuration);
              }
            } catch (error) {
              console.error('Error in YouTube player onReady:', error);
            }
          },
          onStateChange: (event: any) => {
            // Handle state changes if needed
            console.log('YouTube player state changed:', event.data);
          },
          onError: (event: any) => {
            console.error('YouTube player error:', event.data);
            // Handle different error codes
            switch (event.data) {
              case 2:
                console.error('Invalid video ID');
                break;
              case 5:
                console.error('HTML5 player error');
                break;
              case 100:
                console.error('Video not found');
                break;
              case 101:
              case 150:
                console.error('Video cannot be embedded');
                break;
              default:
                console.error('Unknown YouTube player error');
            }
          },
        },
      });
    } catch (error) {
      console.error('Failed to initialize YouTube player:', error);
      // Optionally provide fallback or user notification
    }
  };

  // Update current time for YouTube videos (only for owner)
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isOwner && isYouTubeVideo && youTubePlayerRef.current && isPlaying) {
      interval = setInterval(() => {
        try {
          if (youTubePlayerRef.current &&
              typeof youTubePlayerRef.current.getCurrentTime === 'function') {
            const time = youTubePlayerRef.current.getCurrentTime();
            if (typeof time === 'number' && !isNaN(time)) {
              setCurrentTime(time);
            }
          }
        } catch (error) {
          console.error('Error getting YouTube player current time:', error);
        }
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isOwner, isYouTubeVideo, isPlaying, setCurrentTime]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('video/') && isOwner) {
      const url = URL.createObjectURL(file);
      setVideoUrl(url, 'file');
    }
  };

  const handleUrlSubmit = () => {
    if (inputUrl.trim() && isOwner) {
      const trimmedUrl = inputUrl.trim();

      // Check if it's a YouTube URL
      if (isYouTubeUrl(trimmedUrl)) {
        const videoId = extractYouTubeVideoId(trimmedUrl);
        if (videoId) {
          setVideoUrl(trimmedUrl, 'youtube', videoId);
          setShowUrlInput(false);
          setInputUrl("");
          return;
        }
      }

      // Handle regular video URLs
      setVideoUrl(trimmedUrl, 'url');
      setShowUrlInput(false);
      setInputUrl("");
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current && isOwner) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && isOwner) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleSeek = (time: number) => {
    if (isOwner) {
      try {
        if (isYouTubeVideo && youTubePlayerRef.current &&
            typeof youTubePlayerRef.current.seekTo === 'function') {
          youTubePlayerRef.current.seekTo(time, true);
        } else if (videoRef.current) {
          videoRef.current.currentTime = time;
        }
        setCurrentTime(time);
      } catch (error) {
        console.error('Error seeking video:', error);
      }
    }
  };

  const handlePlayPause = () => {
    if (isOwner) {
      setPlaying(!isPlaying);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    try {
      if (isYouTubeVideo && youTubePlayerRef.current &&
          typeof youTubePlayerRef.current.setVolume === 'function') {
        youTubePlayerRef.current.setVolume(newVolume * 100);
      } else if (videoRef.current) {
        videoRef.current.volume = newVolume;
      }
    } catch (error) {
      console.error('Error changing volume:', error);
    }
  };

  if (!hasVideo) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-slate-900">
        <Card className="w-full max-w-md mx-4 bg-slate-800 border-slate-700">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <Play className="w-8 h-8 text-slate-300" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No Video Loaded</h3>
            <p className="text-slate-400 mb-6">Upload a video file or add a YouTube URL to start watching together.</p>
            
            {isOwner ? (
              <div className="space-y-4">
                <Button 
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full bg-slate-700 hover:bg-slate-600 text-white"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Video File
                </Button>
                
                <Button 
                  onClick={() => setShowUrlInput(!showUrlInput)}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Link className="w-4 h-4 mr-2" />
                  Add Video URL
                </Button>

                {showUrlInput && (
                  <div className="flex gap-2 mt-4">
                    <Input
                      placeholder="Enter YouTube URL or video URL..."
                      value={inputUrl}
                      onChange={(e) => setInputUrl(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                    <Button onClick={handleUrlSubmit} size="sm">
                      Add
                    </Button>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            ) : (
              <p className="text-slate-500 text-sm">Only the room owner can add videos.</p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full bg-black group">
      {isYouTubeVideo ? (
        youTubeApiStatus === 'failed' ? (
          // Fallback iframe embed when API fails to load
          <iframe
            src={`https://www.youtube.com/embed/${youTubeVideoId}?enablejsapi=1&origin=${encodeURIComponent(window.location.origin)}&autoplay=0&controls=1&modestbranding=1&rel=0`}
            className="w-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title="YouTube video player"
          />
        ) : youTubeApiStatus === 'loading' ? (
          // Loading state for YouTube API
          <div className="w-full h-full flex items-center justify-center bg-slate-900">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-white">Loading YouTube player...</p>
            </div>
          </div>
        ) : (
          <div id="youtube-player" className="w-full h-full" />
        )
      ) : (
        <video
          ref={videoRef}
          src={videoState?.video_url}
          className="w-full h-full object-contain"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          controls={false}
        />
      )}
      
      {/* Video Controls Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div className="absolute bottom-0 left-0 right-0 p-6">
          {/* Progress Bar */}
          <div className="mb-4">
            <div className="flex items-center gap-2 text-white text-sm mb-2">
              <span>{formatTime(currentTime)}</span>
              <span>/</span>
              <span>{formatTime(duration)}</span>
            </div>
            <div 
              className="w-full h-2 bg-slate-700 rounded-full cursor-pointer"
              onClick={(e) => {
                if (isOwner && duration > 0) {
                  const rect = e.currentTarget.getBoundingClientRect();
                  const percent = (e.clientX - rect.left) / rect.width;
                  handleSeek(percent * duration);
                }
              }}
            >
              <div 
                className="h-full bg-white rounded-full transition-all duration-150"
                style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {isOwner && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                    className="text-white hover:bg-white/20"
                  >
                    <SkipBack className="w-5 h-5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePlayPause}
                    className="text-white hover:bg-white/20"
                  >
                    {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
                    className="text-white hover:bg-white/20"
                  >
                    <SkipForward className="w-5 h-5" />
                  </Button>
                </>
              )}
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onMuteToggle}
                  className="text-white hover:bg-white/20"
                >
                  {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </Button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={isMuted ? 0 : volume}
                  onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                  className="w-20 h-1 bg-slate-600 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (isYouTubeVideo) {
                  // For YouTube, we'll make the container fullscreen
                  document.getElementById('youtube-player')?.parentElement?.requestFullscreen();
                } else {
                  videoRef.current?.requestFullscreen();
                }
              }}
              className="text-white hover:bg-white/20"
            >
              <Maximize className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
