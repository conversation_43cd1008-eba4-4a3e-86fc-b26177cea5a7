# YouTube CORS Issues - Solutions and Troubleshooting

## Problem
You're experiencing CORS (Cross-Origin Resource Sharing) errors when trying to load the YouTube iframe API from your local development server at `http://localhost:8080`. The errors look like:

```
Access to script at 'https://www.youtube.com/iframe_api' from origin 'http://localhost:8080' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Root Cause
This happens because:
1. YouTube's iframe API has security restrictions for certain origins
2. Local development servers (especially HTTP) can trigger stricter CORS policies
3. Some browsers have enhanced security features that block cross-origin script loading

## Solutions Implemented

### 1. Improved Vite Configuration
Updated `vite.config.ts` with better CORS handling:
- Added proper CORS configuration
- Set appropriate headers for cross-origin requests
- Configured referrer policy

### 2. Enhanced YouTube API Loading
Modified `VideoPlayer.tsx` with:
- Multiple loading strategies with fallbacks
- Better error handling and retry logic
- Automatic fallback to iframe embed when API fails
- Loading state indicators

### 3. Fallback iframe Embed
When the YouTube API fails to load, the app automatically falls back to a direct iframe embed that doesn't require the JavaScript API.

## Testing Your Setup

### Option 1: Use the Test Page
1. Start your development server: `npm run dev`
2. Navigate to: `http://localhost:8080/youtube-test.html`
3. Click "Test API Load" to see if the API loads successfully
4. If it fails, try "Test Direct Embed" to verify iframe embedding works

### Option 2: Test in Your App
1. Start the development server
2. Create a room and try adding a YouTube video
3. The app should now either load the API successfully or fall back to iframe embed

## Additional Solutions to Try

### Solution 1: Use HTTPS for Development
```bash
# Install mkcert for local SSL certificates
npm install -g mkcert
mkcert -install
mkcert localhost 127.0.0.1 ::1

# Update vite.config.ts to use HTTPS
# Set https: true and provide cert paths
```

### Solution 2: Use Different Port
Some networks/firewalls block certain ports. Try changing the port in `vite.config.ts`:
```typescript
server: {
  port: 3000, // or 5173, 8000, etc.
}
```

### Solution 3: Use Different Host
Try using `127.0.0.1` instead of `localhost`:
```bash
# Access your app via:
http://127.0.0.1:8080
```

### Solution 4: Browser-Specific Solutions

#### Chrome
- Start Chrome with disabled security (for development only):
```bash
chrome --disable-web-security --disable-features=VizDisplayCompositor --user-data-dir=/tmp/chrome_dev_session
```

#### Firefox
- Go to `about:config`
- Set `security.tls.insecure_fallback_hosts` to include `localhost`

### Solution 5: Network/Firewall Issues
If you're on a corporate network:
- Check if YouTube domains are blocked
- Try using a VPN
- Contact your network administrator

## Verification Steps

1. **Check Console**: Look for any remaining CORS errors
2. **Test YouTube Videos**: Try adding different YouTube URLs
3. **Test Fallback**: Verify that iframe fallback works when API fails
4. **Test Controls**: Ensure video controls work in both API and fallback modes

## Current Implementation Status

✅ **Implemented:**
- Fallback iframe embed when API fails
- Improved error handling and retry logic
- Better CORS configuration in Vite
- Loading states and user feedback

✅ **Working Features:**
- Automatic detection of YouTube API load failures
- Seamless fallback to iframe embed
- Proper error logging for debugging

## Next Steps

If issues persist:
1. Check the browser's Network tab for specific error details
2. Try the test page to isolate the issue
3. Consider using HTTPS for development
4. Test on different browsers/networks

## Support

If you continue experiencing issues, please provide:
- Browser and version
- Network environment (home/corporate/etc.)
- Specific error messages from browser console
- Results from the test page
