import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Enable HTTPS for local development to avoid CORS issues with YouTube API
    https: false, // Set to true if you have SSL certificates
    cors: {
      origin: true,
      credentials: true,
    },
    headers: {
      // Add headers to help with external API loading
      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Cross-Origin-Opener-Policy': 'unsafe-none',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
    },
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
      },
    },
  },
  // Add build configuration for better external script handling
  build: {
    rollupOptions: {
      external: [],
    },
  },
  // Optimize dependencies to handle external scripts better
  optimizeDeps: {
    exclude: ['youtube-iframe-api'],
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(<PERSON><PERSON>an),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
