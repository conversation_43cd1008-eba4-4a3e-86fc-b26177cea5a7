<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #4CAF50; }
        .error { background-color: #f44336; }
        .warning { background-color: #ff9800; }
        .info { background-color: #2196F3; }
        #player {
            width: 100%;
            height: 400px;
            background-color: #333;
            margin: 20px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YouTube iframe API Test</h1>
        <p>This page tests if the YouTube iframe API can be loaded successfully in your environment.</p>
        
        <div id="status-container">
            <div class="status info">
                <strong>Status:</strong> <span id="status-text">Initializing...</span>
            </div>
        </div>

        <div>
            <button onclick="testAPILoad()">Test API Load</button>
            <button onclick="testDirectEmbed()">Test Direct Embed</button>
            <button onclick="clearPlayer()">Clear Player</button>
        </div>

        <div id="player"></div>

        <div id="log-container">
            <h3>Debug Log:</h3>
            <div id="log" style="background-color: #333; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        let player;
        let apiLoaded = false;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const statusElement = document.getElementById('status-text');
            const statusContainer = document.querySelector('.status');
            
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            
            statusElement.textContent = message;
            statusContainer.className = `status ${type}`;
            
            console.log(`[YouTube Test] ${message}`);
        }

        function testAPILoad() {
            log('Starting YouTube API load test...', 'info');
            
            // Check if API is already loaded
            if (window.YT && window.YT.Player) {
                log('YouTube API already loaded!', 'success');
                createPlayer();
                return;
            }

            // Remove any existing scripts
            const existingScripts = document.querySelectorAll('script[src*="youtube.com/iframe_api"]');
            existingScripts.forEach(script => {
                log('Removing existing script: ' + script.src, 'warning');
                script.remove();
            });

            // Create new script
            const script = document.createElement('script');
            script.src = 'https://www.youtube.com/iframe_api';
            script.async = true;
            
            script.onload = () => {
                log('Script loaded successfully', 'success');
                // Wait for YT object to be available
                checkYTReady();
            };
            
            script.onerror = (error) => {
                log('Script failed to load: ' + error, 'error');
                log('This indicates a CORS or network issue', 'error');
            };

            // Set up global callback
            window.onYouTubeIframeAPIReady = () => {
                log('YouTube API ready callback fired', 'success');
                apiLoaded = true;
                createPlayer();
            };

            document.head.appendChild(script);
            log('Script tag added to document head', 'info');
        }

        function checkYTReady(attempts = 0) {
            if (window.YT && window.YT.Player) {
                log('YT.Player is available', 'success');
                apiLoaded = true;
                createPlayer();
            } else if (attempts < 50) {
                setTimeout(() => checkYTReady(attempts + 1), 100);
            } else {
                log('Timeout waiting for YT.Player', 'error');
            }
        }

        function createPlayer() {
            if (!apiLoaded) {
                log('API not loaded yet', 'warning');
                return;
            }

            try {
                player = new YT.Player('player', {
                    height: '400',
                    width: '100%',
                    videoId: 'dQw4w9WgXcQ', // Rick Roll for testing
                    playerVars: {
                        autoplay: 0,
                        controls: 1,
                        modestbranding: 1,
                        rel: 0,
                        origin: window.location.origin
                    },
                    events: {
                        onReady: (event) => {
                            log('Player ready!', 'success');
                        },
                        onError: (event) => {
                            log('Player error: ' + event.data, 'error');
                        }
                    }
                });
                log('Player created successfully', 'success');
            } catch (error) {
                log('Error creating player: ' + error.message, 'error');
            }
        }

        function testDirectEmbed() {
            log('Testing direct iframe embed...', 'info');
            const playerDiv = document.getElementById('player');
            playerDiv.innerHTML = `
                <iframe 
                    width="100%" 
                    height="400" 
                    src="https://www.youtube.com/embed/dQw4w9WgXcQ?enablejsapi=1&origin=${encodeURIComponent(window.location.origin)}" 
                    style="border: none;"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
                </iframe>
            `;
            log('Direct embed created', 'success');
        }

        function clearPlayer() {
            log('Clearing player...', 'info');
            const playerDiv = document.getElementById('player');
            playerDiv.innerHTML = '';
            if (player && player.destroy) {
                player.destroy();
                player = null;
            }
            log('Player cleared', 'info');
        }

        // Initialize
        log('Page loaded. Click "Test API Load" to begin.', 'info');
    </script>
</body>
</html>
